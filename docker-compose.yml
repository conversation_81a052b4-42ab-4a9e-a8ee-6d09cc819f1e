services:
  app:
    image: docker.eskyspace.com/esky-common/esky-images/dbr/node18:latest
    env_file:
      - docker-compose.env

  orders:
    extends:
      service: app
    hostname: orders
    environment:
      HEALTHCHECK_URL_PATH: '/orders'
    networks:
      default:
        aliases:
          - orders.esky.pl.local

  cli:
    image: docker.eskyspace.com/esky-common/esky-images/dbr/node18-cli:latest
    env_file:
      - .env
      - docker-compose.env
    hostname: cli
    privileged: true
    working_dir: /esky-pps-nx
    environment:
#      SSH_AUTH_SOCK: '${SSH_AUTH_SOCK}'
      SHM_PREPARE: 'no'
    volumes:
      - ./:/esky-pps-nx:cached
#      - '${SSH_AUTH_SOCK}:${SSH_AUTH_SOCK}'
    security_opt:
      - 'seccomp:unconfined'
